# 🚀 MCP快速修复指南

## ❌ 问题诊断
您的MCP服务显示红色状态是因为使用了错误的命令名称。

## ✅ 解决方案

### 第一步：删除现有配置
1. 在MCP界面中关闭所有服务
2. 删除所有现有的MCP服务配置

### 第二步：使用正确配置重新添加

#### 1. Sequential Thinking MCP ⭐ (最重要)
```
Command: npx
Args: @modelcontextprotocol/server-sequential-thinking
```

#### 2. Playwright MCP
```
Command: npx  
Args: @playwright/mcp
```

#### 3. PDF Reader MCP
```
Command: npx
Args: -y @smithery/cli@latest run @sylphxltd/pdf-reader-mcp --key 568cba21-d6e2-4567-9fa2-00d6271cacbf --profile grotesque-impala-Tmp9Dr
```

#### 4. Fetch MCP
```
Command: npx
Args: -y @smithery/cli@latest run fetch-mcp --key 568cba21-d6e2-4567-9fa2-00d6271cacbf
```

#### 5. <PERSON><PERSON><PERSON>ls MCP
```
Command: npx
Args: -y @smithery/cli@latest run @diulela/browser-tools-mcp
```

### 第三步：逐个测试
- 每添加一个服务后等待3-5秒
- 确认状态指示灯变绿后再添加下一个
- 如果某个服务失败，跳过它继续下一个

## 🎯 优先级建议
1. **Sequential Thinking** - 最重要，优先配置
2. **Fetch MCP** - 网络请求功能
3. **PDF Reader** - 文档处理
4. **Playwright** - 浏览器自动化
5. **Browser Tools** - 浏览器工具

## 🔧 故障排除
- 如果服务仍然失败，检查网络连接
- NPX方式的服务首次启动可能需要下载，请耐心等待
- 确保每次只添加一个服务，避免同时启动多个

## ✅ 验证成功
所有服务的状态指示灯都应该显示为绿色。如果有红色，请检查命令和参数是否完全匹配上述配置。
