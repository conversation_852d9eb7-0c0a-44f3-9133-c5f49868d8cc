# MCP配置完整指南

## ✅ 已验证可用的MCP服务

### 1. Sequential Thinking MCP
**状态**: ✅ 已全局安装并测试通过
**配置方式**:
```
Command: npx
Args: @modelcontextprotocol/server-sequential-thinking
```

### 2. Playwright MCP
**状态**: ✅ 已全局安装并测试通过
**配置方式**:
```
Command: npx
Args: @playwright/mcp
```

### 3. PDF Reader MCP (通过Smithery)
**状态**: ✅ NPX方式可用
**配置方式**:
```
Command: npx
Args: -y @smithery/cli@latest run @sylphxltd/pdf-reader-mcp --key 568cba21-d6e2-4567-9fa2-00d6271cacbf --profile grotesque-impala-Tmp9Dr
```

### 4. Fetch MCP (通过Smithery)
**状态**: ✅ NPX方式可用
**配置方式**:
```
Command: npx
Args: -y @smithery/cli@latest run fetch-mcp --key 568cba21-d6e2-4567-9fa2-00d6271cacbf
```

### 5. Browser Tools MCP (通过Smithery)
**状态**: ✅ NPX方式可用
**配置方式**:
```
Command: npx
Args: -y @smithery/cli@latest run @diulela/browser-tools-mcp
```

## 🔧 配置步骤

### 步骤1: 在MCP界面中删除所有现有配置
1. 关闭所有MCP服务
2. 删除所有现有的MCP服务配置

### 步骤2: 按顺序添加MCP服务

#### 2.1 添加Sequential Thinking
- 点击 "Add Stdio MCP"
- Command: `npx`
- Args: `@modelcontextprotocol/server-sequential-thinking`
- 点击保存并启用

#### 2.2 添加Playwright (可选)
- 点击 "Add Stdio MCP"
- Command: `npx`
- Args: `@playwright/mcp`
- 点击保存并启用

#### 2.3 添加PDF Reader
- 点击 "Add Stdio MCP"
- Command: `npx`
- Args: `-y @smithery/cli@latest run @sylphxltd/pdf-reader-mcp --key 568cba21-d6e2-4567-9fa2-00d6271cacbf --profile grotesque-impala-Tmp9Dr`
- 点击保存并启用

#### 2.4 添加Fetch MCP
- 点击 "Add Stdio MCP"
- Command: `npx`
- Args: `-y @smithery/cli@latest run fetch-mcp --key 568cba21-d6e2-4567-9fa2-00d6271cacbf`
- 点击保存并启用

### 步骤3: 测试配置
每添加一个服务后，等待几秒钟查看状态指示灯是否变绿。

## 🚨 故障排除

### 如果服务启动失败:
1. 检查命令拼写是否正确
2. 确保网络连接正常
3. 尝试重启MCP客户端
4. 查看错误日志

### 如果仍有问题:
1. 只启用Sequential Thinking (最重要的)
2. 其他服务可以逐个添加测试
3. 确保每次只添加一个服务

## 📝 备注
- 所有MCP包已全局安装并测试通过
- NPX方式的服务首次启动可能较慢
- 建议优先启用Sequential Thinking和Fetch MCP
