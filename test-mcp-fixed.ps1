Write-Host "=== MCP Services Test (Fixed Version) ===" -ForegroundColor Cyan

Write-Host "`n1. Testing Sequential Thinking MCP (Fixed)..." -ForegroundColor Yellow
try {
    $process = Start-Process -FilePath "npx" -ArgumentList "@modelcontextprotocol/server-sequential-thinking", "--help" -PassThru -NoNewWindow -RedirectStandardOutput "nul" -RedirectStandardError "nul"
    $process.WaitForExit(10000)
    if ($process.ExitCode -eq 0 -or $process.ExitCode -eq 1) {
        Write-Host "✅ Sequential Thinking MCP: WORKING" -ForegroundColor Green
    } else {
        Write-Host "❌ Sequential Thinking MCP: FAILED (Exit Code: $($process.ExitCode))" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Sequential Thinking MCP: ERROR - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n2. Testing Playwright MCP (Fixed)..." -ForegroundColor Yellow
try {
    $process = Start-Process -FilePath "npx" -ArgumentList "@playwright/mcp", "--help" -PassThru -NoNewWindow -RedirectStandardOutput "nul" -RedirectStandardError "nul"
    $process.WaitForExit(10000)
    if ($process.ExitCode -eq 0 -or $process.ExitCode -eq 1) {
        Write-Host "✅ Playwright MCP: WORKING" -ForegroundColor Green
    } else {
        Write-Host "❌ Playwright MCP: FAILED (Exit Code: $($process.ExitCode))" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Playwright MCP: ERROR - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n3. Testing Smithery CLI..." -ForegroundColor Yellow
try {
    $process = Start-Process -FilePath "npx" -ArgumentList "-y", "@smithery/cli@latest", "--help" -PassThru -NoNewWindow -RedirectStandardOutput "nul" -RedirectStandardError "nul"
    $process.WaitForExit(15000)
    if ($process.ExitCode -eq 0) {
        Write-Host "✅ Smithery CLI: WORKING" -ForegroundColor Green
    } else {
        Write-Host "❌ Smithery CLI: FAILED (Exit Code: $($process.ExitCode))" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Smithery CLI: ERROR - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Cyan
Write-Host "`n🔧 CORRECTED MCP Configuration:" -ForegroundColor White
Write-Host "1. Sequential Thinking:" -ForegroundColor Gray
Write-Host "   Command: npx" -ForegroundColor Gray
Write-Host "   Args: @modelcontextprotocol/server-sequential-thinking" -ForegroundColor Gray
Write-Host "`n2. Playwright:" -ForegroundColor Gray
Write-Host "   Command: npx" -ForegroundColor Gray
Write-Host "   Args: @playwright/mcp" -ForegroundColor Gray
Write-Host "`n3. PDF Reader:" -ForegroundColor Gray
Write-Host "   Command: npx" -ForegroundColor Gray
Write-Host "   Args: -y @smithery/cli@latest run @sylphxltd/pdf-reader-mcp --key YOUR_KEY --profile YOUR_PROFILE" -ForegroundColor Gray
Write-Host "`n4. Fetch:" -ForegroundColor Gray
Write-Host "   Command: npx" -ForegroundColor Gray
Write-Host "   Args: -y @smithery/cli@latest run fetch-mcp --key YOUR_KEY" -ForegroundColor Gray
Write-Host "`n5. Browser Tools:" -ForegroundColor Gray
Write-Host "   Command: npx" -ForegroundColor Gray
Write-Host "   Args: -y @smithery/cli@latest run @diulela/browser-tools-mcp" -ForegroundColor Gray

Write-Host "`n📝 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Delete all existing MCP configurations in your MCP client" -ForegroundColor White
Write-Host "2. Add each service using the corrected commands above" -ForegroundColor White
Write-Host "3. Test each service individually before adding the next one" -ForegroundColor White
Write-Host "4. Wait for green status indicators before proceeding" -ForegroundColor White
