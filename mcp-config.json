{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking"], "env": {}, "timeout": 30000}, "playwright": {"command": "npx", "args": ["@playwright/mcp"], "env": {}, "timeout": 30000}, "fetch": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "fetch-mcp", "--key", "568cba21-d6e2-4567-9fa2-00d6271cacbf"], "env": {}, "timeout": 60000}, "pdf-reader": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@sylphxltd/pdf-reader-mcp", "--key", "568cba21-d6e2-4567-9fa2-00d6271cacbf", "--profile", "grotesque-impala-Tmp9Dr"], "env": {}, "timeout": 60000}, "browser-tools": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@diulela/browser-tools-mcp"], "env": {}, "timeout": 60000}}}